@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 210 20% 96%; /* Light Gray #EDF2F7 */
    --foreground: 225 20% 15%; /* Dark Blue #1A202C */

    --card: 0 0% 100%; /* White */
    --card-foreground: 225 20% 15%; /* Dark Blue */

    --popover: 0 0% 100%; /* White */
    --popover-foreground: 225 20% 15%; /* Dark Blue */

    --primary: 225 20% 15%; /* Dark Blue #1A202C */
    --primary-foreground: 210 20% 98%; /* Off-white/Light Gray for text on primary */

    --secondary: 210 20% 90%; /* A slightly darker gray */
    --secondary-foreground: 225 20% 15%; /* Dark Blue */

    --muted: 210 20% 93%; /* Muted Gray */
    --muted-foreground: 225 20% 40%; /* Darker gray for muted text */

    --accent: 174 45% 52%; /* Teal #4DC0B5 */
    --accent-foreground: 0 0% 100%; /* White for text on Teal */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 20% 85%;
    --input: 210 20% 85%;
    --ring: 174 45% 52%; /* Teal for focus rings */

    --radius: 0.5rem;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Sidebar specific theme variables */
    --sidebar-background: 225 20% 12%; /* Darker Blue */
    --sidebar-foreground: 210 20% 85%; /* Lighter Gray */
    --sidebar-primary: 174 45% 52%; /* Teal */
    --sidebar-primary-foreground: 0 0% 100%; /* White */
    --sidebar-accent: 225 20% 20%; /* Hover background for sidebar items */
    --sidebar-accent-foreground: 210 20% 96%; /* Light gray text on hover */
    --sidebar-border: 225 20% 25%;
    --sidebar-ring: 174 45% 52%; /* Teal */
  }

  .dark {
    /* Define dark theme if needed, for now keep light theme focus based on prompt */
    --background: 225 20% 15%; /* Dark Blue */
    --foreground: 210 20% 96%; /* Light Gray */

    --card: 225 20% 12%; /* Darker Blue */
    --card-foreground: 210 20% 96%; /* Light Gray */

    --popover: 225 20% 12%; /* Darker Blue */
    --popover-foreground: 210 20% 96%; /* Light Gray */

    --primary: 174 45% 52%; /* Teal as primary in dark mode */
    --primary-foreground: 225 20% 10%; /* Very Dark Blue for text on Teal */

    --secondary: 225 20% 25%; /* Darker shade for secondary */
    --secondary-foreground: 210 20% 96%; /* Light Gray */

    --muted: 225 20% 20%;
    --muted-foreground: 210 20% 60%;

    --accent: 174 45% 60%; /* Lighter Teal for accent */
    --accent-foreground: 225 20% 10%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 225 20% 25%;
    --input: 225 20% 25%;
    --ring: 174 45% 52%; /* Teal */

    /* Dark Sidebar specific theme variables */
    --sidebar-background: 225 20% 10%; 
    --sidebar-foreground: 210 20% 90%; 
    --sidebar-primary: 174 45% 58%; 
    --sidebar-primary-foreground: 225 20% 8%; 
    --sidebar-accent: 225 20% 18%; 
    --sidebar-accent-foreground: 210 20% 98%; 
    --sidebar-border: 225 20% 22%;
    --sidebar-ring: 174 45% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
