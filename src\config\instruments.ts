import type {
  ForexCommodityInstrumentType,
  VolatilityInstrumentType,
  InstrumentType
} from '../types'; // Adjust path if your types file is elsewhere relative to src/config

/**
 * Array of supported Forex and Commodity instruments.
 * These should match the string literals defined in ForexCommodityInstrumentType.
 */
export const FOREX_COMMODITY_INSTRUMENTS: ForexCommodityInstrumentType[] = [
  'EUR/USD',
  'GBP/USD',
  'XAU/USD',
  'Palladium/USD',
  'Platinum/USD',
  'Silver/USD',
];

/**
 * Array of supported Volatility Index instruments.
 * These should match the string literals defined in VolatilityInstrumentType.
 */
export const VOLATILITY_INSTRUMENTS: VolatilityInstrumentType[] = [
  'Volatility 10 Index',
  'Volatility 25 Index',
  'Volatility 50 Index',
  'Volatility 75 Index',
  'Volatility 100 Index',
  'Volatility 10 (1s) Index',
  'Volatility 25 (1s) Index',
  'Volatility 50 (1s) Index',
  'Volatility 75 (1s) Index',
  'Volatility 100 (1s) Index',
];

/**
 * Represents a supported instrument with its type.
 */
export interface SupportedInstrument {
  id: InstrumentType;
  name: string; // User-friendly name
  type: 'Forex' | 'Commodity' | 'Volatility';
  defaultDecimalPlaces: number;
}

/**
 * Comprehensive list of all supported instruments by the platform.
 * This array is used throughout the application to populate instrument selectors,
 * fetch data, and guide AI anlysis.
 */
export const SUPPORTED_INSTRUMENTS: SupportedInstrument[] = [
  // Forex
  { id: 'EUR/USD', name: 'EUR/USD', type: 'Forex', defaultDecimalPlaces: 5 },
  { id: 'GBP/USD', name: 'GBP/USD', type: 'Forex', defaultDecimalPlaces: 5 },
  // Commodities
  { id: 'XAU/USD', name: 'Gold (XAU/USD)', type: 'Commodity', defaultDecimalPlaces: 2 },
  { id: 'Palladium/USD', name: 'Palladium/USD', type: 'Commodity', defaultDecimalPlaces: 2 },
  { id: 'Platinum/USD', name: 'Platinum/USD', type: 'Commodity', defaultDecimalPlaces: 2 },
  { id: 'Silver/USD', name: 'Silver/USD', type: 'Commodity', defaultDecimalPlaces: 4 },
  // Volatility Indices
  { id: 'Volatility 10 Index', name: 'Volatility 10 Index', type: 'Volatility', defaultDecimalPlaces: 3 },
  { id: 'Volatility 25 Index', name: 'Volatility 25 Index', type: 'Volatility', defaultDecimalPlaces: 3 },
  { id: 'Volatility 50 Index', name: 'Volatility 50 Index', type: 'Volatility', defaultDecimalPlaces: 2 },
  { id: 'Volatility 75 Index', name: 'Volatility 75 Index', type: 'Volatility', defaultDecimalPlaces: 4 },
  { id: 'Volatility 100 Index', name: 'Volatility 100 Index', type: 'Volatility', defaultDecimalPlaces: 2 },
  // 1-Second Volatility Indices
  { id: 'Volatility 10 (1s) Index', name: 'Volatility 10 (1s) Index', type: 'Volatility', defaultDecimalPlaces: 3 },
  { id: 'Volatility 25 (1s) Index', name: 'Volatility 25 (1s) Index', type: 'Volatility', defaultDecimalPlaces: 3 },
  { id: 'Volatility 50 (1s) Index', name: 'Volatility 50 (1s) Index', type: 'Volatility', defaultDecimalPlaces: 2 },
  { id: 'Volatility 75 (1s) Index', name: 'Volatility 75 (1s) Index', type: 'Volatility', defaultDecimalPlaces: 4 },
  { id: 'Volatility 100 (1s) Index', name: 'Volatility 100 (1s) Index', type: 'Volatility', defaultDecimalPlaces: 2 },
];

/**
 * Default instrument to be selected when the application loads or when an invalid instrument is chosen.
 */
export const DEFAULT_INSTRUMENT: InstrumentType = FOREX_COMMODITY_INSTRUMENTS[0];

/**
 * Helper function to get a specific supported instrument object.
 * @param instrumentId The ID of the instrument (InstrumentType).
 * @returns The SupportedInstrument object or undefined if not found.
 */
export function getSupportedInstrument(instrumentId: InstrumentType): SupportedInstrument | undefined {
  return SUPPORTED_INSTRUMENTS.find(inst => inst.id === instrumentId);
} 